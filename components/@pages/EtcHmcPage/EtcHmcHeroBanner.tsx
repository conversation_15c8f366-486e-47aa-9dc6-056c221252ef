import React from 'react';
import { Text } from '@checkatrade/web-ui';
import { HeadingXXL } from 'lib/design-system';
import { EtcHmcFeatureList } from './EtcHmcFeatureList';

interface EtcHmcHeroBannerProps {
  pageType: 'etc' | 'hmc';
  title: string;
  subtitle: string;
  heroImage: string;
  heroImageAlt: string;
}

const getBackgroundColor = (pageType: 'etc' | 'hmc') => {
  return pageType === 'etc' ? 'bg-[#FF3F3F]' : 'bg-[#040154]';
};

const getHeroContent = (pageType: 'etc' | 'hmc') => {
  if (pageType === 'etc') {
    return {
      title: "Too busy to find a trade? We'll help!",
      subtitle:
        "When urgent jobs arise, get connected to an expert who's ready to help. We'll do the search for you",
    };
  }

  return {
    title: 'Welcome to the neighbourhood',
    subtitle:
      "We're here to help make your move smoother — with trusted local tradespeople at your fingertips",
  };
};

export const EtcHmcHeroBanner: React.FC<EtcHmcHeroBannerProps> = ({
  pageType,
  heroImage,
  heroImageAlt,
}) => {
  const { title, subtitle } = getHeroContent(pageType);
  const backgroundColorClass = getBackgroundColor(pageType);

  return (
    <section
      className={`relative w-full ${backgroundColorClass} overflow-hidden`}
    >
      <div className="mx-auto max-w-7xl px-4 py-12 md:py-16 lg:py-20">
        <div className="grid grid-cols-1 items-center gap-8 lg:grid-cols-2 lg:gap-12">
          {/* Text Content */}
          <div className="text-white">
            <HeadingXXL
              as="h1"
              className="mb-6 text-4xl font-bold leading-tight text-white md:text-5xl lg:text-6xl"
            >
              {title}
            </HeadingXXL>
            <Text
              variant="bodyMD"
              className="mb-8 max-w-lg text-lg leading-relaxed text-white/90 md:text-xl"
            >
              {subtitle}
            </Text>

            {/* Feature List integrated into the text section */}
            <EtcHmcFeatureList pageType={pageType} />
          </div>

          {/* Hero Image */}
          <div className="relative flex justify-center lg:justify-end">
            <div className="relative aspect-square size-80 overflow-hidden rounded-full md:size-96 lg:size-[400px]">
              <img
                src={heroImage}
                alt={heroImageAlt}
                className="size-full object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
