import formatPageTitle from '@utils/formatters/page-title';
import routerPushResetScroll from '@utils/router-push-reset-scroll';
import {
  getEtcHmcPageName,
  getEtcHmcPageSlug,
} from 'components/@pages/EtcHmcPage/page-name-utils';
import { SearchContentContainer } from 'components/@pages/SearchPage/SearchContent';
import { Wrapper } from 'components/@pages/SearchPage/SearchPage';
import {
  SearchPageContent,
  TotalResults,
} from 'components/@pages/SearchPage/styles';
import LocationScreen from 'components/Funnelling/screens/LocationScreen';
import { HeadingXXL } from 'lib/design-system';
import { EtcHmcHeroBanner } from './EtcHmcHeroBanner';
import type { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import Head from 'next/head';
import { useRouter } from 'next/router';

type EtcHmcTopLevelPageProps = {
  pageName: string;
  pageSlug: string;
};

export const getServerSideProps = (async ({ req }) => ({
  props: {
    pageName: getEtcHmcPageName(req.url),
    pageSlug: getEtcHmcPageSlug(req.url),
  },
})) satisfies GetServerSideProps<EtcHmcTopLevelPageProps>;

const getPageType = (pageSlug: string): 'etc' | 'hmc' => {
  return pageSlug === 'Emergency-Trades' ? 'etc' : 'hmc';
};

const getHeroImage = (pageType: 'etc' | 'hmc') => {
  // Using existing images from the public/static directory as placeholders
  // These should be replaced with the actual hero images from the designs
  return pageType === 'etc'
    ? '/static/handy-man.png' // Red page - tradesperson working image
    : '/static/builder-on-phone.png'; // Blue page - family moving/unpacking image
};

export const EtcHmcTopLevelPage = ({
  pageName,
  pageSlug,
}: InferGetServerSidePropsType<
  GetServerSideProps<EtcHmcTopLevelPageProps>
>) => {
  const router = useRouter();
  const pageType = getPageType(pageSlug);
  const heroImage = getHeroImage(pageType);

  const handleSearchSubmit = (location: string) => {
    const etcHmcLocation = location.trim().toUpperCase().replace(/\s+/g, '-');
    routerPushResetScroll(router, `/Search/${pageSlug}/in/${etcHmcLocation}`);
  };

  return (
    <>
      <Head>
        <title>{formatPageTitle(pageName)}</title>
        <meta
          name="description"
          content={`Find trusted ${pageName} on Checkatrade`}
        />
        <meta name="robots" content="noindex" />
      </Head>

      {/* New Hero Banner with integrated feature list */}
      <EtcHmcHeroBanner
        pageType={pageType}
        title=""
        subtitle=""
        heroImage={heroImage}
        heroImageAlt={`${pageName} hero image`}
      />

      {/* Search Section */}
      <Wrapper>
        <TotalResults>
          <HeadingXXL as="h2">
            Find trusted {pageType === 'etc' ? 'trades' : 'home movers'} in RH10
            (4)
          </HeadingXXL>
        </TotalResults>
        <SearchContentContainer>
          <SearchPageContent>
            <LocationScreen
              buttonText="Search"
              onComplete={handleSearchSubmit}
              onBack={() => {}} // No back action needed but required by types
              hideGoBack
            />
          </SearchPageContent>
        </SearchContentContainer>
        <div className="h-[200px]" />
      </Wrapper>
    </>
  );
};
