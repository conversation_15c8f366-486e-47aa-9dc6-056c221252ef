import React from 'react';
import { Text } from '@checkatrade/web-ui';
import { IconCheckCircleFill } from 'lib/design-system';

interface FeatureItem {
  id: string;
  icon: React.ReactNode;
  title: string;
  description: string;
}

interface EtcHmcFeatureListProps {
  pageType: 'etc' | 'hmc';
}

const getFeatures = (pageType: 'etc' | 'hmc'): FeatureItem[] => {
  if (pageType === 'etc') {
    return [
      {
        id: 'browse',
        icon: <IconCheckCircleFill size="medium" color="#ffffff" />,
        title: 'Browse',
        description: 'shortlisted top trades in your area',
      },
      {
        id: 'explore',
        icon: <IconCheckCircleFill size="medium" color="#ffffff" />,
        title: 'Explore',
        description: 'profiles, read reviews and get in touch',
      },
      {
        id: 'call',
        icon: <IconCheckCircleFill size="medium" color="#ffffff" />,
        title: 'Call',
        description: "our support team - we're here to help",
      },
    ];
  }

  return [
    {
      id: 'discover',
      icon: <IconCheckCircleFill size="medium" color="#ffffff" />,
      title: 'Discover',
      description: 'trusted local tradespeople',
    },
    {
      id: 'browse',
      icon: <IconCheckCircleFill size="medium" color="#ffffff" />,
      title: 'Browse',
      description: 'verified profiles and reviews',
    },
    {
      id: 'call',
      icon: <IconCheckCircleFill size="medium" color="#ffffff" />,
      title: 'Call',
      description: "our support team - we're here to help",
    },
  ];
};

export const EtcHmcFeatureList: React.FC<EtcHmcFeatureListProps> = ({
  pageType,
}) => {
  const features = getFeatures(pageType);

  return (
    <div className="mt-8 w-full max-w-xl">
      <div className="space-y-3 md:space-y-4">
        {features.map(feature => (
          <div
            key={feature.id}
            className="flex items-center gap-4 rounded-full bg-white/10 px-4 py-3 backdrop-blur-sm md:px-6 md:py-4"
          >
            {/* Circular Icon Container */}
            <div className="flex size-10 shrink-0 items-center justify-center rounded-full bg-white/20 md:size-12">
              {feature.icon}
            </div>

            {/* Text Content */}
            <div className="min-w-0 flex-1">
              <Text
                variant="bodyMD"
                className="text-sm text-white md:text-base"
              >
                <span className="font-bold">{feature.title}</span>{' '}
                <span className="font-normal">{feature.description}</span>
              </Text>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
