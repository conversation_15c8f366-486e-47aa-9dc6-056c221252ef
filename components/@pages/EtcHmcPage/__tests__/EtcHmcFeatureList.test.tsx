import React from 'react';
import { render, screen } from '@testing-library/react';
import { EtcHmcFeatureList } from '../EtcHmcFeatureList';

describe('EtcHmcFeatureList', () => {
  it('renders ETC page features correctly', () => {
    render(<EtcHmcFeatureList pageType="etc" />);

    expect(screen.getByText('Browse')).toBeVisible();
    expect(
      screen.getByText('shortlisted top trades in your area'),
    ).toBeVisible();
    expect(screen.getByText('Explore')).toBeVisible();
    expect(
      screen.getByText('profiles, read reviews and get in touch'),
    ).toBeVisible();
    expect(screen.getByText('Call')).toBeVisible();
    expect(
      screen.getByText("our support team - we're here to help"),
    ).toBeVisible();
  });

  it('renders HMC page features correctly', () => {
    render(<EtcHmcFeatureList pageType="hmc" />);

    expect(screen.getByText('Discover')).toBeVisible();
    expect(screen.getByText('trusted local tradespeople')).toBeVisible();
    expect(screen.getByText('Browse')).toBeVisible();
    expect(screen.getByText('verified profiles and reviews')).toBeVisible();
    expect(screen.getByText('Call')).toBeVisible();
    expect(
      screen.getByText("our support team - we're here to help"),
    ).toBeVisible();
  });

  it('renders correct number of features', () => {
    const { container } = render(<EtcHmcFeatureList pageType="etc" />);

    const featureItems = container.querySelectorAll(
      '[class*="flex items-center gap-4"]',
    );
    expect(featureItems).toHaveLength(3);
  });

  it('renders circular icon containers', () => {
    const { container } = render(<EtcHmcFeatureList pageType="etc" />);

    const iconContainers = container.querySelectorAll(
      '[class*="rounded-full"]',
    );
    expect(iconContainers.length).toBeGreaterThan(0);
  });
});
