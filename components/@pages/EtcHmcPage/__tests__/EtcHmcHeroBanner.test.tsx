import React from 'react';
import { render, screen } from '@testing-library/react';
import { EtcHmcHeroBanner } from '../EtcHmcHeroBanner';

describe('EtcHmcHeroBanner', () => {
  const defaultProps = {
    pageType: 'etc' as const,
    title: 'Test Title',
    subtitle: 'Test Subtitle',
    heroImage: '/test-image.jpg',
    heroImageAlt: 'Test Alt Text',
  };

  it('renders ETC page content correctly', () => {
    render(<EtcHmcHeroBanner {...defaultProps} pageType="etc" />);

    expect(
      screen.getByText("Too busy to find a trade? We'll help!"),
    ).toBeVisible();
    expect(
      screen.getByText(
        "When urgent jobs arise, get connected to an expert who's ready to help. We'll do the search for you",
      ),
    ).toBeVisible();
  });

  it('renders HMC page content correctly', () => {
    render(<EtcHmcHeroBanner {...defaultProps} pageType="hmc" />);

    expect(screen.getByText('Welcome to the neighbourhood')).toBeVisible();
    expect(
      screen.getByText(
        "We're here to help make your move smoother — with trusted local tradespeople at your fingertips",
      ),
    ).toBeVisible();
  });

  it('renders hero image with correct alt text', () => {
    render(<EtcHmcHeroBanner {...defaultProps} />);

    const image = screen.getByAltText('Test Alt Text');
    expect(image).toBeVisible();
    expect(image).toHaveAttribute('src', '/test-image.jpg');
  });

  it('applies correct background color for ETC page', () => {
    const { container } = render(
      <EtcHmcHeroBanner {...defaultProps} pageType="etc" />,
    );

    const section = container.querySelector('section');
    expect(section).toHaveClass('bg-[#FF3F3F]');
  });

  it('applies correct background color for HMC page', () => {
    const { container } = render(
      <EtcHmcHeroBanner {...defaultProps} pageType="hmc" />,
    );

    const section = container.querySelector('section');
    expect(section).toHaveClass('bg-[#040154]');
  });
});
