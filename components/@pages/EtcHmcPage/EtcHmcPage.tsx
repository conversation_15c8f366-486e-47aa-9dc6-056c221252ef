import { Accordion, <PERSON> } from '@checkatrade/web-ui';
import formatPageTitle from '@utils/formatters/page-title';
import { modifySearchItems } from '@utils/modify-search-items';
import { isFullPostcode, isPostcodeSector } from '@utils/postcode-validation';
import { EtcHmcFooterBanner } from 'components/@pages/EtcHmcPage/EtcHmcFooterBanner';
import { createGetRandomPostcode } from 'components/@pages/EtcHmcPage/random-postcode-api';
import {
  ScreenReaderOnlyH2,
  SearchContentContainer,
} from 'components/@pages/SearchPage/SearchContent';
import { SearchList } from 'components/@pages/SearchPage/SearchList';
import { SearchNoResults } from 'components/@pages/SearchPage/SearchNoResults';
import { List, TotalResults } from 'components/@pages/SearchPage/styles';
import { HeadingXXL } from 'lib/design-system';
import { matchEtcHmc, type matchCampaignResponse } from 'lib/sdks/search';
import { getDeviceId } from 'lib/utils/device';
import type { GetServerSideProps, InferGetServerSidePropsType } from 'next';
import Head from 'next/head';

type EtcHmcPageParams = {
  location: string; // either a postcode sector (e.g. BN1 1) or a full postcode (e.g. BN1 1NH) - Whitespace might have been replaced by dash
};

type EtcHmcPageQueryParams = {
  editionId: string;
};

type EtcHmcPageProps = {
  errors: EtcHmcPageError[];
  pageName: string;
  location: string;
  source: string;
  searchResults: matchCampaignResponse;
};

enum EtcHmcPageError {
  NoValidPostcode = 'NO_VALID_POSTCODE',
  NoSearchApi = 'NO_SEARCH_API',
  NoSearchResults = 'NO_SEARCH_RESULTS',
}

const COLLATOR = new Intl.Collator(undefined, { sensitivity: 'base' });

const isEtcPage = (url?: string): boolean =>
  url?.includes(`/Search/Emergency-Trades/in`) ?? false;
const isHmcPage = (url?: string): boolean =>
  url?.includes('/Search/My-Home-Movers') ?? false;

export const getServerSideProps = (async ({ req, params, query }) => {
  const { location } = params as EtcHmcPageParams;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { editionId } = query as EtcHmcPageQueryParams;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const deviceId = req?.cookies['cat-device-id'] ?? getDeviceId();

  const errors: EtcHmcPageError[] = [];

  const isPageEtc = isEtcPage(req.url);
  const isPageHmc = isHmcPage(req.url);

  let pageName: string = '';
  if (isPageEtc) {
    pageName = 'Trades';
  } else if (isPageHmc) {
    pageName = 'Home Movers';
  }

  // get full postcode
  const maybePostcode = location.replace('-', ' ');
  let fullPostcode = '';

  if (isFullPostcode(maybePostcode)) {
    fullPostcode = maybePostcode;
  } else if (isPostcodeSector(maybePostcode)) {
    try {
      // pick a random full postcode
      const getRandomPostcode = await createGetRandomPostcode(maybePostcode);
      const randomPostcodeResponse = await getRandomPostcode({});
      fullPostcode = randomPostcodeResponse[0].postcode;
    } catch (_) {
      errors.push(EtcHmcPageError.NoValidPostcode);
    }
  } else {
    errors.push(EtcHmcPageError.NoValidPostcode);
  }

  // get search results
  let searchResultData: matchCampaignResponse = [];

  if (!errors.includes(EtcHmcPageError.NoValidPostcode)) {
    try {
      searchResultData = await matchEtcHmc(
        fullPostcode,
        {
          page: 1,
          size: 20,
          algorithm: isPageEtc ? 'etc' : 'hmc',
        },
        deviceId,
      );
      searchResultData.sort((a, b) =>
        COLLATOR.compare(a.categoryInfo.label, b.categoryInfo.label),
      );
      if (searchResultData.length === 0) {
        errors.push(EtcHmcPageError.NoSearchResults);
      }
    } catch (matchEtcError) {
      errors.push(EtcHmcPageError.NoSearchApi);
    }
  }

  return {
    props: {
      errors,
      pageName,
      location: maybePostcode,
      source: isPageEtc ? 'etc' : 'hm',
      searchResults: searchResultData.map(item => ({
        categoryInfo: item.categoryInfo,
        searchResults: modifySearchItems(item.searchResults),
      })),
    },
  };
}) satisfies GetServerSideProps<EtcHmcPageProps>;

export function EtcHmcPage({
  errors,
  pageName,
  location,
  source,
  searchResults,
}: InferGetServerSidePropsType<GetServerSideProps<EtcHmcPageProps>>) {
  const title = `${pageName} in ${location}`;

  return (
    <>
      <Head>
        <title>{formatPageTitle(title)}</title>
        <meta
          name="description"
          content={`Find trusted ${pageName} in ${location}`}
        />
        <meta name="robots" content="noindex" />
      </Head>
      <div className="flex size-full flex-col items-center justify-start">
        {/* section : title */}
        <TotalResults>
          <HeadingXXL as="h1">
            {errors.includes(EtcHmcPageError.NoValidPostcode) && pageName}
            {!errors.includes(EtcHmcPageError.NoValidPostcode) && (
              <>
                {title}{' '}
                {!errors.includes(EtcHmcPageError.NoSearchResults) &&
                  `(${searchResults.length})`}
              </>
            )}
          </HeadingXXL>
        </TotalResults>
        {/* section : content */}
        <SearchContentContainer>
          {errors.includes(EtcHmcPageError.NoValidPostcode) && (
            <SearchNoResults
              titleClassName="text-center"
              title={
                <>
                  Invalid postcode <i>{location}</i>
                </>
              }
              suggestion={
                <>
                  Search again on{' '}
                  <Link className="underline" href="/">
                    Checkatrade.com
                  </Link>
                </>
              }
            />
          )}
          {errors.includes(EtcHmcPageError.NoSearchApi) && (
            <SearchNoResults
              titleClassName="text-center"
              title={
                <>
                  Your {pageName} Page is coming very soon - keep your card
                  handy for when you need us.
                </>
              }
              suggestion={
                <>
                  In the meantime please visit{' '}
                  <Link href="https://www.checkatrade.com/">
                    Checkatrade.com
                  </Link>
                </>
              }
            />
          )}
          {errors.includes(EtcHmcPageError.NoSearchResults) && (
            <>
              <ScreenReaderOnlyH2>Search results</ScreenReaderOnlyH2>
              <SearchNoResults
                titleClassName="text-center"
                title={<>No trades found for this search.</>}
                suggestion={
                  <>
                    Search again on <Link href="/">Checkatrade.com</Link>
                  </>
                }
              />
            </>
          )}
          {errors.length === 0 && (
            <>
              <ScreenReaderOnlyH2>Search results</ScreenReaderOnlyH2>
              {searchResults.map(data => (
                <div key={data.categoryInfo.id} style={{ width: '100% ' }}>
                  <Accordion
                    title={data.categoryInfo.label}
                    subtitle={`${data.searchResults.items.length} trades`}
                    startOpen={false}
                  >
                    <List data-guid="SearchList">
                      <SearchList
                        operatingLocationText={location}
                        items={data.searchResults.items}
                        // eslint-disable-next-line @typescript-eslint/no-unused-vars
                        onTrackEvent={(...args: unknown[]) => {
                          return;
                        }}
                        page={1}
                        loading={false}
                        itemsPerPage={data.searchResults.items.length}
                        postcode={location}
                        categoryLabel={
                          data.categoryInfo.parent?.label ||
                          data.categoryInfo.label
                        }
                        categoryId={data.categoryInfo.id}
                        location={location}
                        source={source}
                        // eslint-disable-next-line @typescript-eslint/no-unused-vars
                        onSetModal={(...args: unknown[]) => {
                          return;
                        }}
                        // eslint-disable-next-line @typescript-eslint/no-unused-vars
                        onMessageClick={(...args: unknown[]) => {
                          return;
                        }}
                        hideContactMessageCta
                      />
                    </List>
                  </Accordion>
                  <br />
                </div>
              ))}
            </>
          )}
        </SearchContentContainer>
        <EtcHmcFooterBanner />
      </div>
    </>
  );
}
